import { resolve } from 'node:path';
import { defineConfig } from 'vitest/config';

// https://cn.vitest.dev/guide/
export default defineConfig({
  resolve: {
    alias: {
      src: resolve(__dirname, 'src'),
    },
  },
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['../../vitest.setup.ts'],
    include: [
      'src/**/tests/*.{test,spec}.?(c|m)[jt]s?(x)',
      'src/**/__tests__/*.{test,spec}.?(c|m)[jt]s?(x)'
    ],
    testTimeout: 15000, // Increase timeout for complex async tests
    coverage: {
      provider: 'istanbul',
      include: ['src/**/*.ts'],
    },
  },
});
