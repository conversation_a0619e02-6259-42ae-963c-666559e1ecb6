import { describe, expect, test, it, beforeEach, afterEach, beforeAll, afterAll, vi } from 'vitest';
import type { RenderHookResult } from '@testing-library/react';
import { act, renderHook, waitFor } from '@testing-library/react';
import useRequest from '../index';

// Create a simple mock request function that works better with fake timers
const mockRequest = vi.fn((req: any) => {
  return new Promise<string>((resolve, reject) => {
    setTimeout(() => {
      if (req === 0) {
        reject(new Error('fail'));
      } else {
        resolve('success');
      }
    }, 100); // Shorter timeout for faster tests
  });
});

const errorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

describe('useRequest', () => {
  beforeEach(() => {
    mockRequest.mockClear();
  });

  afterAll(() => {
    errorSpy.mockRestore();
  });

  const setUp = (
    service: Parameters<typeof useRequest>[0],
    options: Parameters<typeof useRequest>[1],
  ) => renderHook((o) => useRequest(service, o || options));

  let hook: RenderHookResult<any, any>;

  test('useRequest should auto run', async () => {
    let value;
    let success;
    const successCallback = (text: string) => {
      success = text;
    };
    const errorCallback = vi.fn();
    const beforeCallback = () => {
      value = 'before';
    };
    const finallyCallback = () => {
      value = 'finally';
    };
    // auto run success
    act(() => {
      hook = setUp(mockRequest, {
        onSuccess: successCallback,
        onError: errorCallback,
        onBefore: beforeCallback,
        onFinally: finallyCallback,
      });
    });
    expect(hook.result.current.loading).toBe(true);
    expect(value).toBe('before');
    expect(success).toBeUndefined();

    await waitFor(() => expect(hook.result.current.loading).toBe(false), { timeout: 5000 });
    expect(success).toBe('success');
    expect(hook.result.current.data).toBe('success');
    expect(value).toBe('finally');
    expect(errorCallback).toHaveBeenCalledTimes(0);

    //manual run fail
    act(() => {
      hook.result.current.run(0);
    });
    expect(hook.result.current.loading).toBe(true);

    await waitFor(() => expect(hook.result.current.error).toEqual(new Error('fail')), { timeout: 5000 });
    expect(hook.result.current.loading).toBe(false);
    expect(errorCallback).toHaveBeenCalledTimes(1);

    //manual run success
    act(() => {
      hook.result.current.run(1);
    });
    expect(hook.result.current.loading).toBe(true);

    await waitFor(() => expect(hook.result.current.data).toBe('success'), { timeout: 5000 });
    await waitFor(() => expect(hook.result.current.loading).toBe(false), { timeout: 5000 });
    expect(errorCallback).toHaveBeenCalledTimes(1);
    hook.unmount();

    //auto run fail
    act(() => {
      hook = setUp(() => mockRequest(0), {
        onSuccess: successCallback,
        onError: errorCallback,
      });
    });
    expect(hook.result.current.loading).toBe(true);

    await act(async () => {
      vi.advanceTimersByTime(100);
    });
    await waitFor(() => expect(hook.result.current.error).toEqual(new Error('fail')), { timeout: 1000 });
    expect(hook.result.current.loading).toBe(false);
    expect(errorCallback).toHaveBeenCalledTimes(2);
    hook.unmount();
  });

  test('useRequest should be manually triggered', async () => {
    act(() => {
      hook = setUp(mockRequest, {
        manual: true,
      });
    });
    expect(hook.result.current.loading).toBe(false);
    act(() => {
      hook.result.current.run(1);
    });
    expect(hook.result.current.loading).toBe(true);

    act(() => {
      vi.runAllTimers();
    });
    await waitFor(() => expect(hook.result.current.loading).toBe(false));
    expect(hook.result.current.data).toBe('success');
    act(() => {
      hook.result.current.run(0);
    });
    expect(hook.result.current.loading).toBe(true);

    act(() => {
      vi.runAllTimers();
    });
    await waitFor(() => expect(hook.result.current.loading).toBe(false));
    expect(hook.result.current.error).toEqual(new Error('fail'));
    hook.unmount();
  });

  test('useRequest runAsync should work', async () => {
    let success = '',
      error = '';

    act(() => {
      hook = setUp(mockRequest, {
        manual: true,
      });
    });
    act(() => {
      hook.result.current
        .runAsync(0)
        .then((res) => {
          success = res;
        })
        .catch((err) => {
          error = err;
        });
    });

    act(() => {
      vi.runAllTimers();
    });
    expect(success).toBe('');
    await waitFor(() => expect(error).toEqual(new Error('fail')));
    success = '';
    error = '';
    act(() => {
      hook.result.current
        .runAsync(1)
        .then((res) => {
          success = res;
        })
        .catch((err) => {
          error = err;
        });
    });

    act(() => {
      vi.runAllTimers();
    });
    await waitFor(() => expect(success).toBe('success'));
    expect(error).toBe('');
    hook.unmount();
  });

  test('useRequest mutate should work', async () => {
    act(() => {
      hook = setUp(mockRequest, {});
    });

    act(() => {
      vi.runAllTimers();
    });
    await waitFor(() => expect(hook.result.current.data).toBe('success'));
    act(() => {
      hook.result.current.mutate('hello');
    });
    expect(hook.result.current.data).toBe('hello');
    hook.unmount();
  });

  test('useRequest defaultParams should work', async () => {
    act(() => {
      hook = setUp(mockRequest, {
        defaultParams: [1, 2, 3],
      });
    });
    expect(hook.result.current.loading).toBe(true);

    act(() => {
      vi.runAllTimers();
    });
    expect(hook.result.current.params).toEqual([1, 2, 3]);
    await waitFor(() => expect(hook.result.current.data).toBe('success'));
    expect(hook.result.current.loading).toBe(false);
    hook.unmount();
  });
});
